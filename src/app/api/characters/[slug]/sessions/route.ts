import { NextResponse } from "next/server";

import type { LLMModel } from "@/generated/prisma";
import { MessageRole } from "@/generated/prisma";
import { findChatModel } from "@/lib/chatModel";
import { getCharacterForRead } from "@/lib/permissions";
import { prisma } from "@/lib/prisma";
import {
  getCurrentUserPersona,
  replacePersonaAndCharacterPlaceholders,
  resolvePersonaStrings,
} from "@/lib/userPersona";
import { auth } from "@/auth";

type RouteParams = {
  params: Promise<{
    slug: string;
  }>;
};

type SessionMessage = {
  id: string;
  role: "assistant" | "user";
  content: string;
  createdAt: string;
};

type SessionPayload = {
  chatSession: {
    id: string;
    createdAt: string;
    updatedAt: string;
    model: {
      id: string;
      displayName: string;
    } | null;
    messages: SessionMessage[];
  };
};

const mapRole = (role: MessageRole): "assistant" | "user" =>
  role === MessageRole.ASSISTANT ? "assistant" : "user";

const buildSessionPayload = (
  session: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    model: Pick<LLMModel, "id" | "displayName"> | null;
    messages: Array<{
      id: string;
      role: MessageRole;
      content: string;
      createdAt: Date;
    }>;
  },
): SessionPayload => ({
  chatSession: {
    id: session.id,
    createdAt: session.createdAt.toISOString(),
    updatedAt: session.updatedAt.toISOString(),
    model: session.model
      ? {
        id: session.model.id,
        displayName: session.model.displayName,
      }
      : null,
    messages: session.messages
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      .map((message) => ({
        id: message.id,
        role: mapRole(message.role),
        content: message.content,
        createdAt: message.createdAt.toISOString(),
      })),
  },
});

export async function GET(_: Request, { params }: RouteParams) {
  const { slug } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await getCharacterForRead({
    slug,
    select: {
      id: true,
      name: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const userPersona = await getCurrentUserPersona({
    userId: userSession.user.id!,
    userName: userSession.user.name,
  });

  const personaStrings = resolvePersonaStrings(userPersona);

  const sessions = await prisma.chatSession.findMany({
    where: { characterId: character.id },
    orderBy: { updatedAt: "desc" },
    select: {
      id: true,
      createdAt: true,
      updatedAt: true,
      model: {
        select: {
          id: true,
          displayName: true,
        },
      },
      _count: {
        select: {
          messages: true,
        },
      },
      messages: {
        orderBy: { createdAt: "desc" },
        select: {
          content: true,
          role: true,
        },
        take: 1,
      },
    },
  });

  return NextResponse.json({
    chatSessions: sessions.map((session) => {
      const latestMessage = session.messages[0];
      const preview = latestMessage
        ? latestMessage.role === MessageRole.ASSISTANT
          ?
              replacePersonaAndCharacterPlaceholders(
                latestMessage.content,
                personaStrings,
                character.name,
              ) ?? latestMessage.content
          : latestMessage.content
        : null;

      return {
        id: session.id,
        createdAt: session.createdAt.toISOString(),
        updatedAt: session.updatedAt.toISOString(),
        model: session.model
          ? { id: session.model.id, displayName: session.model.displayName }
          : null,
        messageCount: session._count.messages,
        lastMessagePreview: preview,
      };
    }),
  });
}

export async function POST(request: Request, { params }: RouteParams) {
  const { slug } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await getCharacterForRead({
    slug,
    select: {
      id: true,
      name: true,
      firstMessage: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const userPersona = await getCurrentUserPersona({
    userId: userSession.user.id!,
    userName: userSession.user.name,
  });

  const personaStrings = resolvePersonaStrings(userPersona);

  let greetingOverride: string | null = null;
  let requestedModelId: string | null = null;

  if (request.headers.get("content-type")?.includes("application/json")) {
    try {
      const body = (await request.json()) as { greeting?: unknown; modelId?: unknown };
      if (typeof body?.greeting === "string") {
        const trimmed = body.greeting.trim();
        greetingOverride = trimmed.length
          ? replacePersonaAndCharacterPlaceholders(trimmed, personaStrings, character.name)
          : null;
      }
      if (typeof body?.modelId === "string" && body.modelId.trim().length > 0) {
        requestedModelId = body.modelId.trim();
      }
    } catch {
      // Ignore malformed JSON bodies; fall back to default greeting
    }
  }

  const model = await findChatModel({ requestedModelId });

  if (!model) {
    return NextResponse.json(
      { error: "No chat models are configured. Add one before creating a session." },
      { status: 503 },
    );
  }

  if (requestedModelId && model.id !== requestedModelId) {
    return NextResponse.json(
      { error: "Requested chat model was not found." },
      { status: 400 },
    );
  }

  const chatSession = await prisma.chatSession.create({
    data: {
      characterId: character.id,
      userId: userSession.user.id!,
      modelId: model.id,
    },
  });

  const initialGreeting =
    greetingOverride && greetingOverride.length > 0
      ? greetingOverride
      : replacePersonaAndCharacterPlaceholders(
          character.firstMessage?.trim() ?? null,
          personaStrings,
          character.name,
        );

  if (initialGreeting) {
    await prisma.chatMessage.create({
      data: {
        chatSessionId: chatSession.id,
        role: MessageRole.ASSISTANT,
        content: initialGreeting,
      },
    });
  }

  const refreshedSession = await prisma.chatSession.update({
    where: { id: chatSession.id },
    data: { updatedAt: new Date() },
    include: {
      messages: true,
      model: {
        select: {
          id: true,
          displayName: true,
        },
      },
    },
  });

  const payload = buildSessionPayload({
    id: refreshedSession.id,
    createdAt: refreshedSession.createdAt,
    updatedAt: refreshedSession.updatedAt,
    model: refreshedSession.model,
    messages: refreshedSession.messages,
  });

  return NextResponse.json(payload);
}
